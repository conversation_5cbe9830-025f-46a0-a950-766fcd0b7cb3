package com.wanlianyida.support.domain.service;

import com.wanlianyida.support.domain.model.condition.SignConfigCondition;
import com.wanlianyida.support.domain.model.entity.SignConfigEntity;
import com.wanlianyida.support.domain.repository.SignConfigRepository;
import com.wanlianyida.support.infrastructure.exception.SupportException;
import com.wanlianyida.support.infrastructure.exception.SupportExceptionEnum;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年06月25日 16:18
 */
@Service
public class SignConfigDomainService {

    @Resource
    private SignConfigRepository signConfigRepository;

    public void insertSignConfig(SignConfigEntity entity) {
        if (!signConfigRepository.insertSignConfig(entity)) {
            throw new SupportException(SupportExceptionEnum.SIGN_CONFIG_ADD_FAIL);
        }
    }

    public SignConfigEntity updateSignConfig(SignConfigEntity entity) {
        signConfigRepository.updateSignConfig(entity);
        return signConfigRepository.queryById(entity.getId());
    }

    public void deleteSignConfig(Long id) {
        if (!signConfigRepository.deleteSignConfig(id)) {
            throw new SupportException(SupportExceptionEnum.SIGN_CONFIG_DELETE_FAIL);
        }
    }

    public SignConfigEntity queryById(Long id) {
        return signConfigRepository.queryById(id);
    }

    public List<SignConfigEntity> queryCondition(SignConfigCondition condition) {
        return signConfigRepository.queryCondition(condition);
    }
}
