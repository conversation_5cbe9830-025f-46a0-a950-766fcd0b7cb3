package com.wanlianyida.support.infrastructure.exception;

import lombok.Getter;

@Getter
public enum SupportExceptionEnum {

    API_NOT_FOUND("BU-SP-001", "接口不存在"),
    API_ALREADY_EXISTS("BU-SP-002", "接口已存在，请勿重复添加"),
    API_ADD_FAIL("BU-SP-003", "接口添加失败"),
    API_UPDATE_FAIL("BU-SP-004", "接口更新失败"),
    API_DELETE_FAIL("BU-SP-005", "接口删除失败"),
    API_ALTER_CONTENT_BLANK("BU-SP-006", "当前接口版本有变更，请输入变更内容"),

    SIGN_CONFIG_NOT_FOUND("BU-SP-011", "签名配置不存在"),
    SIGN_CONFIG_ADD_FAIL("BU-SP-012", "签名配置添加失败"),
    SIGN_CONFIG_UPDATE_FAIL("BU-SP-013", "签名配置更新失败"),
    SIGN_CONFIG_DELETE_FAIL("BU-SP-014", "签名配置删除失败"),
    ;

    private String code;

    private String msg;

    SupportExceptionEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
