package com.wanlianyida.support.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年07月24日 19:38
 */
@Data
@ApiModel("用户配置关系")
@TableName("platform_um_user_config")
public class UserConfigPO extends Model<UserConfigPO> {

    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    private String userBaseId;

    private String type;

    private String value;

    private String createBy;

    private String createName;

    private Date createDate;

    private String updateBy;

    private String updateName;

    private Date updateDate;

    private String delFlag;
}
