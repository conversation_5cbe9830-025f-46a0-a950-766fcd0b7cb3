package com.wanlianyida.support.application.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年07月25日 10:48
 */
@Data
public class WorkbenchDateScopeCommand {

    @NotBlank(message = "用户ID不能为空")
    private String userBaseId;

    @NotBlank(message = "时间范围不能为空")
    @ApiModelProperty("时间范围[10-近30天,20-本月,30-近一周,40-本周,50-昨天,60今天]")
    private String value;
}
