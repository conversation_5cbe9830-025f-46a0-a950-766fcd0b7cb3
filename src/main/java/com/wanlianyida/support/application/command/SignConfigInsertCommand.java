package com.wanlianyida.support.application.command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年06月24日 17:52
 */
@Data
@ApiModel("签名配置表")
public class SignConfigInsertCommand {

    @ApiModelProperty("配置key[客户端:client_web,client_android,client_ios,client_wechat_mp;内部服务:service_ctsp]")
    @NotBlank(message = "配置key不能为空")
    private String configKey;

    @ApiModelProperty("启用标志[0-禁用,1-启用]")
    @NotNull(message = "启用标志不能为空")
    private Integer enableFlag;

    @ApiModelProperty("签名描述")
    @Length(max = 50, message = "签名描述不能超过50个字符")
    private String signDesc;
}
