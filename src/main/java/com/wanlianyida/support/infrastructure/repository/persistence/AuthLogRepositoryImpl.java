package com.wanlianyida.support.infrastructure.repository.persistence;

import cn.hutool.core.bean.BeanUtil;
import com.wanlianyida.support.domain.model.entity.AuthLogEntity;
import com.wanlianyida.support.domain.repository.AuthLogRepository;
import com.wanlianyida.support.infrastructure.repository.mapper.AuthLogMapper;
import com.wanlianyida.support.infrastructure.repository.po.AuthLogPO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年06月25日 15:32
 */
@Repository
public class AuthLogRepositoryImpl implements AuthLogRepository {

    @Resource
    private AuthLogMapper authLogMapper;

    @Override
    public boolean insertAuthLog(AuthLogEntity entity) {
        return authLogMapper.insert(BeanUtil.toBean(entity, AuthLogPO.class)) > 0;
    }
}
