package com.wanlianyida.support.application.assembler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.wanlianyida.framework.lgicommon.entity.TokenInfo;
import com.wanlianyida.framework.lgicore.utils.JwtUtil;
import com.wanlianyida.support.application.command.ApiManageInsertCommand;
import com.wanlianyida.support.application.command.ApiManageUpdateCommand;
import com.wanlianyida.support.domain.model.bo.ApiManageBO;
import com.wanlianyida.support.domain.model.entity.ApiManageEntity;
import com.wanlianyida.support.domain.model.entity.ApiVersionRecordEntity;
import com.wanlianyida.support.infrastructure.exception.SupportException;
import com.wanlianyida.support.infrastructure.exception.SupportExceptionEnum;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年06月26日 09:53
 */
public class ApiManageAssembler {

    public static ApiManageBO insertApi(ApiManageInsertCommand command) {
        ApiManageBO bo = new ApiManageBO();
        ApiManageEntity apiManage = BeanUtil.toBean(command, ApiManageEntity.class);
        apiManage.setEnableFlag(1);
        apiManage.setDelFlag(0);
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        Date now = new Date();
        apiManage.setCreatorId(tokenInfo.getUserBaseId());
        apiManage.setUpdaterId(tokenInfo.getUserBaseId());
        apiManage.setCreatedDate(now);
        apiManage.setUpdatedDate(now);
        ApiVersionRecordEntity apiVersionRecord = new ApiVersionRecordEntity();
        apiVersionRecord.setAlterVersion(apiManage.getApiCurVersion());
        apiVersionRecord.setAlterContent("新增接口");
        apiVersionRecord.setUpdaterName(command.getApiOwner());
        apiVersionRecord.setCreatorId(tokenInfo.getUserBaseId());
        apiVersionRecord.setUpdaterId(tokenInfo.getUserBaseId());
        apiVersionRecord.setCreatedDate(now);
        apiVersionRecord.setUpdatedDate(now);
        bo.setApiManage(apiManage);
        bo.setApiVersionRecord(apiVersionRecord);
        return bo;
    }

    public static ApiManageBO updateApi(ApiManageUpdateCommand command, ApiManageEntity query) {
        ApiManageBO bo = new ApiManageBO();
        ApiManageEntity apiManage = BeanUtil.toBean(command, ApiManageEntity.class);
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        Date now = new Date();
        apiManage.setUpdaterId(tokenInfo.getUserBaseId());
        apiManage.setUpdatedDate(now);
        bo.setApiManage(apiManage);
        if (!query.getApiCurVersion().equals(command.getApiCurVersion())) {
            if (StrUtil.isBlank(command.getAlterContent())) {
                throw new SupportException(SupportExceptionEnum.API_ALTER_CONTENT_BLANK);
            }
            ApiVersionRecordEntity apiVersionRecord = new ApiVersionRecordEntity();
            apiVersionRecord.setApiId(query.getId());
            apiVersionRecord.setAlterVersion(apiManage.getApiCurVersion());
            apiVersionRecord.setAlterContent(command.getAlterContent());
            apiVersionRecord.setUpdaterName(command.getApiOwner());
            apiVersionRecord.setCreatorId(tokenInfo.getUserBaseId());
            apiVersionRecord.setUpdaterId(tokenInfo.getUserBaseId());
            apiVersionRecord.setCreatedDate(now);
            apiVersionRecord.setUpdatedDate(now);
            apiVersionRecord.setUpdaterName(command.getUpdateName());
            bo.setApiVersionRecord(apiVersionRecord);
        }
        return bo;
    }
}
