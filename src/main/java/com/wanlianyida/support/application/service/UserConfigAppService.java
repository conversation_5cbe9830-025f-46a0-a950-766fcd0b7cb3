package com.wanlianyida.support.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.wanlianyida.support.application.command.WorkbenchDateScopeCommand;
import com.wanlianyida.support.application.dto.UserConfigDTO;
import com.wanlianyida.support.application.query.UserConfigQuery;
import com.wanlianyida.support.domain.model.entity.UserConfigEntity;
import com.wanlianyida.support.domain.service.UserConfigDomainService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年07月25日 10:46
 */
@Service
public class UserConfigAppService {

    @Resource
    private UserConfigDomainService userConfigDomainService;

    /**
     * 保存3pl工作台时间范围
     */
    public void saveWorkbenchDateScope(WorkbenchDateScopeCommand command) {
        UserConfigEntity entity = BeanUtil.toBean(command, UserConfigEntity.class);
        entity.setType("30");
        entity.setDelFlag("0");
        userConfigDomainService.saveWorkbenchDateScope(entity);
    }

    /**
     * 获取3pl工作台时间范围
     */
    public UserConfigDTO getWorkbenchDateScope(UserConfigQuery query) {
        List<UserConfigEntity> list = userConfigDomainService.queryByUserId(query.getUserBaseId(), query.getType());
        if (CollectionUtil.isNotEmpty(list)) {
            return BeanUtil.toBean(list.get(0), UserConfigDTO.class);
        }
        return null;
    }
}
