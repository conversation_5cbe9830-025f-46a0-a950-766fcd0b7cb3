package com.wanlianyida.support.domain.service;

import cn.hutool.core.collection.CollectionUtil;
import com.wanlianyida.support.domain.model.bo.ApiManageBO;
import com.wanlianyida.support.domain.model.condition.ApiManageCondition;
import com.wanlianyida.support.domain.model.entity.ApiManageEntity;
import com.wanlianyida.support.domain.model.entity.ApiVersionRecordEntity;
import com.wanlianyida.support.domain.model.entity.AuthLogEntity;
import com.wanlianyida.support.domain.repository.ApiManageRepository;
import com.wanlianyida.support.domain.repository.AuthLogRepository;
import com.wanlianyida.support.infrastructure.exception.SupportException;
import com.wanlianyida.support.infrastructure.exception.SupportExceptionEnum;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年06月25日 16:04
 */
@Service
public class ApiManageDomainService {

    @Resource
    private ApiManageRepository apiManageRepository;
    @Resource
    private AuthLogRepository authLogRepository;

    @Transactional(rollbackFor = Exception.class)
    public void insertApi(ApiManageBO bo) {
        if (verifyApiExists(bo.getApiManage())) {
            throw new SupportException(SupportExceptionEnum.API_ALREADY_EXISTS);
        }
        if (!apiManageRepository.insertApi(bo.getApiManage())) {
            throw new SupportException(SupportExceptionEnum.API_ADD_FAIL);
        }
        bo.getApiVersionRecord().setApiId(bo.getApiManage().getId());
        apiManageRepository.insertRecord(bo.getApiVersionRecord());
    }

    @Transactional(rollbackFor = Exception.class)
    public ApiManageEntity updateApi(ApiManageBO bo) {
        apiManageRepository.updateApi(bo.getApiManage());
        if (bo.getApiVersionRecord() != null) {
            apiManageRepository.insertRecord(bo.getApiVersionRecord());
        }
        return apiManageRepository.queryById(bo.getApiManage().getId());
    }

    public void deleteApi(Long id) {
        if (!apiManageRepository.deleteApi(id)) {
            throw new SupportException(SupportExceptionEnum.API_DELETE_FAIL);
        }
    }

    public ApiManageEntity queryById(Long id) {
        return apiManageRepository.queryById(id);
    }

    public List<ApiManageEntity> queryCondition(ApiManageCondition condition) {
        return apiManageRepository.queryCondition(condition);
    }

    public List<ApiVersionRecordEntity> queryRecordList(Long id) {
        return apiManageRepository.queryRecordList(id);
    }

    public void insertAuthLog(AuthLogEntity entity) {
        authLogRepository.insertAuthLog(entity);
    }

    public boolean verifyApiExists(ApiManageEntity entity) {
        ApiManageCondition condition = new ApiManageCondition();
        condition.setApiUri(entity.getApiUri());
        return CollectionUtil.isNotEmpty(apiManageRepository.queryCondition(condition));
    }
}
