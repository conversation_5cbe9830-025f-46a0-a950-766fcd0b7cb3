package com.wanlianyida.support.domain.repository;

import com.wanlianyida.support.domain.model.entity.UserConfigEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年07月25日 09:09
 */
public interface UserConfigRepository {

    boolean insert(UserConfigEntity entity);

    boolean updateByUserId(UserConfigEntity entity);

    List<UserConfigEntity> queryByUserId(String userId, String type);
}
