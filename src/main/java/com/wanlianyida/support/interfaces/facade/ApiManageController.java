package com.wanlianyida.support.interfaces.facade;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.framework.lgicommon.model.command.IdCommand;
import com.wanlianyida.framework.lgicommon.model.query.IdQuery;
import com.wanlianyida.support.application.command.ApiManageInsertCommand;
import com.wanlianyida.support.application.command.ApiManageUpdateCommand;
import com.wanlianyida.support.application.dto.ApiManageDTO;
import com.wanlianyida.support.application.dto.ApiVersionRecordDTO;
import com.wanlianyida.support.application.query.ApiManageQuery;
import com.wanlianyida.support.application.service.ApiManageAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年06月26日 14:37
 */
@Api("接口管理")
@RestController
@RequestMapping("/api-manage")
public class ApiManageController {

    @Resource
    private ApiManageAppService apiManageAppService;

    @ApiOperation("接口添加")
    @PostMapping("/insert")
    public ResultMode<?> insertApi(@RequestBody @Validated ApiManageInsertCommand command) {
        apiManageAppService.insertApi(command);
        return ResultMode.success();
    }

    @ApiOperation("接口更新")
    @PostMapping("/update")
    public ResultMode<?> updateApi(@RequestBody @Validated ApiManageUpdateCommand command) {
        apiManageAppService.updateApi(command);
        return ResultMode.success();
    }

    @ApiOperation("接口删除")
    @PostMapping("/delete")
    public ResultMode<?> deleteApi(@RequestBody @Validated IdCommand command) {
        apiManageAppService.deleteApi(command);
        return ResultMode.success();
    }

    @ApiOperation("接口分页列表")
    @PostMapping("/page-condition")
    public ResultMode<ApiManageDTO> pageCondition(@RequestBody @Validated PagingInfo<ApiManageQuery> pageQuery) {
        return apiManageAppService.pageCondition(pageQuery);
    }

    @ApiOperation("接口版本记录分页列表")
    @PostMapping("/page-record")
    public ResultMode<ApiVersionRecordDTO> pageRecord(@RequestBody @Validated PagingInfo<IdQuery> pageQuery) {
        return apiManageAppService.pageRecord(pageQuery);
    }
}
