package com.wanlianyida.support.interfaces.facade;

import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.support.application.command.WorkbenchDateScopeCommand;
import com.wanlianyida.support.application.dto.UserConfigDTO;
import com.wanlianyida.support.application.query.UserConfigQuery;
import com.wanlianyida.support.application.service.UserConfigAppService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年07月25日 11:06
 */
@Api(tags = "用户配置关系接口")
@RestController
@RequestMapping("/user-config")
public class UserConfigController {

    @Resource
    private UserConfigAppService userConfigAppService;

    @ApiOperation("保存工作台时间范围")
    @PostMapping("/save-workbench-date-scope")
    public ResultMode<?> saveWorkbenchDateScope(@Validated @RequestBody WorkbenchDateScopeCommand command) {
        userConfigAppService.saveWorkbenchDateScope(command);
        return ResultMode.success();
    }

    @ApiOperation("获取工作台时间范围")
    @PostMapping("/get-workbench-date-scope")
    public ResultMode<UserConfigDTO> getWorkbenchDateScope(@Validated @RequestBody UserConfigQuery query) {
        UserConfigDTO result = userConfigAppService.getWorkbenchDateScope(query);
        return ResultMode.success(result);
    }
}
