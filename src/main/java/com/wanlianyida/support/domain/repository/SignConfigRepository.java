package com.wanlianyida.support.domain.repository;

import com.wanlianyida.support.domain.model.condition.SignConfigCondition;
import com.wanlianyida.support.domain.model.entity.SignConfigEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年06月25日 15:34
 */
public interface SignConfigRepository {

    boolean insertSignConfig(SignConfigEntity entity);

    boolean updateSignConfig(SignConfigEntity entity);

    boolean deleteSignConfig(Long id);

    SignConfigEntity queryById(Long id);

    List<SignConfigEntity> queryCondition(SignConfigCondition condition);
}
