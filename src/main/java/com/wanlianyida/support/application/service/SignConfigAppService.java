package com.wanlianyida.support.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanlianyida.framework.lgicache.impl.RedisService;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.framework.lgicommon.entity.TokenInfo;
import com.wanlianyida.framework.lgicommon.model.command.IdCommand;
import com.wanlianyida.framework.lgicore.utils.JwtUtil;
import com.wanlianyida.support.application.command.SignConfigInsertCommand;
import com.wanlianyida.support.application.command.SignConfigUpdateCommand;
import com.wanlianyida.support.application.dto.SignConfigCacheDTO;
import com.wanlianyida.support.application.dto.SignConfigDTO;
import com.wanlianyida.support.application.query.SignConfigQuery;
import com.wanlianyida.support.domain.model.condition.SignConfigCondition;
import com.wanlianyida.support.domain.model.entity.SignConfigEntity;
import com.wanlianyida.support.domain.service.SignConfigDomainService;
import com.wanlianyida.support.infrastructure.constant.RedisKeyConstant;
import com.wanlianyida.support.infrastructure.exception.SupportException;
import com.wanlianyida.support.infrastructure.exception.SupportExceptionEnum;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年06月26日 09:46
 */
@Service
public class SignConfigAppService {

    @Resource
    private SignConfigDomainService signConfigDomainService;
    @Resource
    private RedisService redisService;

    public void insertSignConfig(SignConfigInsertCommand command) {
        SignConfigEntity entity = BeanUtil.toBean(command, SignConfigEntity.class);
        // 生成签名密钥
        entity.setAccessKeySecret(UUID.randomUUID().toString());
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        Date now = new Date();
        entity.setCreatorId(tokenInfo.getUserBaseId());
        entity.setUpdaterId(tokenInfo.getUserBaseId());
        entity.setCreatedDate(now);
        entity.setUpdatedDate(now);
        signConfigDomainService.insertSignConfig(entity);
        // 添加缓存
        redisService.hSet(RedisKeyConstant.REDIS_KEY_SIGN_CONFIG, entity.getConfigKey(), JSONObject.toJSONString(BeanUtil.toBean(entity, SignConfigCacheDTO.class)));
    }

    public void updateSignConfig(SignConfigUpdateCommand command) {
        SignConfigEntity query = signConfigDomainService.queryById(command.getId());
        if (query == null) {
            throw new SupportException(SupportExceptionEnum.SIGN_CONFIG_NOT_FOUND);
        }
        SignConfigEntity entity = BeanUtil.toBean(command, SignConfigEntity.class);
        TokenInfo tokenInfo = JwtUtil.getTokenInfo();
        Date now = new Date();
        entity.setUpdaterId(tokenInfo.getUserBaseId());
        entity.setUpdatedDate(now);
        SignConfigEntity update = signConfigDomainService.updateSignConfig(entity);
        if (query.getEnableFlag().equals(1) && entity.getEnableFlag().equals(0)) {
            // 删除缓存
            redisService.hDel(RedisKeyConstant.REDIS_KEY_SIGN_CONFIG, update.getConfigKey());
        } else {
            // 更新缓存
            SignConfigEntity newData = signConfigDomainService.queryById(command.getId());
            redisService.hSet(RedisKeyConstant.REDIS_KEY_SIGN_CONFIG, newData.getConfigKey(), JSONObject.toJSONString(BeanUtil.toBean(newData, SignConfigCacheDTO.class)));
        }
    }

    public void deleteSignConfig(IdCommand command) {
        SignConfigEntity query = signConfigDomainService.queryById(command.getId());
        if (query == null) {
            throw new SupportException(SupportExceptionEnum.SIGN_CONFIG_NOT_FOUND);
        }
        signConfigDomainService.deleteSignConfig(command.getId());
        // 删除缓存
        redisService.hDel(RedisKeyConstant.REDIS_KEY_SIGN_CONFIG, query.getConfigKey());
    }

    public ResultMode<SignConfigDTO> pageCondition(PagingInfo<SignConfigQuery> pageQuery) {
        Page<SignConfigEntity> page = PageHelper.startPage(pageQuery.getCurrentPage(), pageQuery.getPageLength(), true);
        page.setOrderBy("updated_date desc");
        List<SignConfigEntity> list = signConfigDomainService.queryCondition(BeanUtil.toBean(pageQuery.getFilterModel(), SignConfigCondition.class));
        return ResultMode.success(BeanUtil.copyToList(list, SignConfigDTO.class), (int) page.getTotal());
    }
}
