package com.wanlianyida.support.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年06月24日 17:53
 */
@Data
@ApiModel("认证日志表")
@TableName("plf_bd_auth_log")
public class AuthLogPO extends Model<AuthLogPO> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("请求参数")
    private String requestParam;

    @ApiModelProperty("服务端签名")
    private String serverSign;

    @ApiModelProperty("客户端签名")
    private String clientSign;

    @ApiModelProperty("认证结果:[10-白名单验证失败,20-toke验证失败,21-token过期,30-签名验证失败,40-验证成功]")
    private Integer authResult;

    @ApiModelProperty("用户ip")
    private String userIp;

    @ApiModelProperty("客户端类型")
    private String clientType;

    @ApiModelProperty("客户端版本")
    private String clientVersion;

    @ApiModelProperty("设备id")
    private String deviceId;

    @ApiModelProperty("接口版本号")
    private String apiVersion;

    @ApiModelProperty("请求地址")
    private String requestUrl;

    private String creatorId;

    private Date createdDate;

    private String updaterId;

    private Date updatedDate;
}
