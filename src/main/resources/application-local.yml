register-center-ip-port: eureka.delta.10000da.vip:9000
eureka:
  client:
    fetch-registry: true
    register-with-eureka: false
    service-url:
      defaultZone: http://${register-center-ip-port}/eureka/
  instance:
    instance-id: ${spring.cloud.client.ip-address}:${spring.application.name}:${server.port}
    lease-expiration-duration-in-seconds: 10
    lease-renewal-interval-in-seconds: 5
    metadata-map:
      management:
        context-path: ${server.servlet.context-path}/actuator
    prefer-ip-address: true
database-base-url: ************************************************************************
spring:
  cache:
    caffeine:
      spec: initialCapacity=50,maximumSize=10000,expireAfterWrite=600s
    type: caffeine
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    username: sit_user
    password: thoh1ayieZ
    url: ${database-base-url}/inner_wlyd_logistics?allowMultiQueries=true&useUnicode=true&characterEncoding=UTF-8&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
    druid:
      filters: stat,log4j2
      initialSize: 5
      maxActive: 20
      maxPoolPreparedStatementPerConnectionSize: 20
      maxWait: 60000
      minEvictableIdleTimeMillis: 300000
      minIdle: 5
      poolPreparedStatements: true
      testOnBorrow: false
      testOnReturn: false
      testWhileIdle: true
      timeBetweenEvictionRunsMillis: 60000
      validationQuery: SELECT 1 FROM DUAL
      stat:
        mergeSql: true
        slowSqlMillis: 5000
  kafka:
    bootstrap-servers: kafka.delta.10000da.vip:30700
    consumer:
      auto-offset-reset: earliest
      enable-auto-commit: true
      group-id: delta_support
      max-poll-records: 200
    producer:
      batch-size: 16384
      buffer-memory: 33554432
      linger: 10
      retries: 3
  redis:
    host: *************
    port: 6379
    maxActive: 200
    maxIdle: 20
    maxWait: 3000
    minIdle: 5
    password: wlyd2019
    testOnBorrow: true
    testOnReturn: true
    timeout: 3000
