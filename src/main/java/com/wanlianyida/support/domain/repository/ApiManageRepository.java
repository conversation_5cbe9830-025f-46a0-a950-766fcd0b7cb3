package com.wanlianyida.support.domain.repository;

import com.wanlianyida.support.domain.model.condition.ApiManageCondition;
import com.wanlianyida.support.domain.model.entity.ApiManageEntity;
import com.wanlianyida.support.domain.model.entity.ApiVersionRecordEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年06月25日 15:34
 */
public interface ApiManageRepository {

    boolean insertApi(ApiManageEntity entity);

    boolean updateApi(ApiManageEntity entity);

    boolean deleteApi(Long id);

    ApiManageEntity queryById(Long id);

    List<ApiManageEntity> queryCondition(ApiManageCondition  condition);

    List<ApiVersionRecordEntity> queryRecordList(Long id);

    boolean insertRecord(ApiVersionRecordEntity entity);
}
