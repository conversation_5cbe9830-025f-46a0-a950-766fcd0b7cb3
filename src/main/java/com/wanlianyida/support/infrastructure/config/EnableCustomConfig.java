package com.wanlianyida.support.infrastructure.config;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.lang.annotation.*;

@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@MapperScan({"com.wanlianyida.support.infrastructure.repository.mapper"})
@EnableAspectJAutoProxy(exposeProxy = true)
@EnableAsync
@EnableDiscoveryClient
@EnableTransactionManagement
@EnableScheduling
@EnableFeignClients
public @interface EnableCustomConfig {
}
