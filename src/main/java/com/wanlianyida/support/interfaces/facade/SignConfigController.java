package com.wanlianyida.support.interfaces.facade;

import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.framework.lgicommon.model.command.IdCommand;
import com.wanlianyida.support.application.command.SignConfigInsertCommand;
import com.wanlianyida.support.application.command.SignConfigUpdateCommand;
import com.wanlianyida.support.application.dto.SignConfigDTO;
import com.wanlianyida.support.application.query.SignConfigQuery;
import com.wanlianyida.support.application.service.SignConfigAppService;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年06月26日 14:50
 */
@RestController
@RequestMapping("/sign-config")
public class SignConfigController {

    @Resource
    private SignConfigAppService signConfigAppService;

    @ApiOperation("签名配置添加")
    @PostMapping("/insert")
    public ResultMode<?> insertSignConfig(@RequestBody @Validated SignConfigInsertCommand command) {
        signConfigAppService.insertSignConfig(command);
        return ResultMode.success();
    }

    @ApiOperation("签名配置更新")
    @PostMapping("/update")
    public ResultMode<?> updateSignConfig(@RequestBody @Validated SignConfigUpdateCommand command) {
        signConfigAppService.updateSignConfig(command);
        return ResultMode.success();
    }

    @ApiOperation("签名配置删除")
    @PostMapping("/delete")
    public ResultMode<?> deleteSignConfig(@RequestBody @Validated IdCommand command) {
        signConfigAppService.deleteSignConfig(command);
        return ResultMode.success();
    }

    @ApiOperation("签名配置分页列表")
    @PostMapping("/page-condition")
    public ResultMode<SignConfigDTO> pageCondition(@RequestBody @Validated PagingInfo<SignConfigQuery> pageQuery) {
        return signConfigAppService.pageCondition(pageQuery);
    }
}
