package com.wanlianyida.support.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年06月24日 17:51
 */
@Data
@ApiModel("接口管理表")
@TableName("plf_bd_api_manage")
public class ApiManagePO extends Model<ApiManagePO> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("产品类型[lgi_app_driver(司机端),lgi_app_shipper(货主端),lgi_3pl(web端),lgi_4pl(web端)]")
    private String productType;

    @ApiModelProperty("服务名称")
    private String serviceName;

    @ApiModelProperty("接口uri")
    private String apiUri;

    @ApiModelProperty("接口名称")
    private String apiName;

    @ApiModelProperty("接口描述")
    private String apiDesc;

    @ApiModelProperty("接口归属人")
    private String apiOwner;

    @ApiModelProperty("需要token鉴权标志[10-不需要,20-需要]")
    private Integer needTokenAuthFlag;

    @ApiModelProperty("需要签名验证标志[10-不需要,20-需要]")
    private Integer needSignVerifyFlag;

    @ApiModelProperty("接口调用方")
    private String apiCaller;

    @ApiModelProperty("接口最小版本号")
    private String apiMinVersion;

    @ApiModelProperty("接口最大版本号")
    private String apiMaxVersion;

    @ApiModelProperty("接口当前版本号")
    private String apiCurVersion;

    @ApiModelProperty("启用标志[0-禁用,1-启用]")
    private Integer enableFlag;

    @ApiModelProperty("删除标志[0-未删除,1-已删除]")
    private Integer delFlag;

    private String creatorId;

    private Date createdDate;

    private String updaterId;

    private Date updatedDate;
}
