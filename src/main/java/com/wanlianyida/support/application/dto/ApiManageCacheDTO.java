package com.wanlianyida.support.application.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年06月24日 17:51
 */
@Data
@ApiModel("接口管理表")
public class ApiManageCacheDTO {

    @ApiModelProperty("产品类型[lgi_app_driver(司机端),lgi_app_shipper(货主端),lgi_3pl(web端),lgi_4pl(web端)]")
    private String productType;

    @ApiModelProperty("服务名称")
    private String serviceName;

    @ApiModelProperty("接口uri")
    private String apiUri;

    @ApiModelProperty("需要token鉴权标志[10-不需要,20-需要]")
    private Integer needTokenAuthFlag;

    @ApiModelProperty("需要签名验证标志[10-不需要,20-需要]")
    private Integer needSignVerifyFlag;

    @ApiModelProperty("接口调用方")
    private String apiCaller;

    @ApiModelProperty("接口最小版本号")
    private String apiMinVersion;

    @ApiModelProperty("接口最大版本号")
    private String apiMaxVersion;

    @ApiModelProperty("接口当前版本号")
    private String apiCurVersion;
}
