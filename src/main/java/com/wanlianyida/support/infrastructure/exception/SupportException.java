package com.wanlianyida.support.infrastructure.exception;

import com.wanlianyida.framework.lgicommon.enums.ResultCode;
import com.wanlianyida.framework.lgicore.exception.WlydException;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024年11月19日 15:46
 */
public class SupportException extends WlydException {
    private static final long serialVersionUID = 1L;

    private String msg;

    private String code = ResultCode.FAILED.getCode();

    @Override
    public String getMessage() {
        return super.getMessage();
    }

    public SupportException() {
        super();
    }

    public SupportException(String msg) {
        super(msg);
        this.msg = msg;
    }

    public SupportException(String msg, String code) {
        super(msg);
        this.msg = msg;
        this.code = code;
    }


    public SupportException(String msg, String code, Throwable e) {
        super(msg, e);
        this.msg = msg;
        this.code = code;
    }

    public SupportException(String msg, Throwable e) {
        super(msg, e);
        this.msg = msg;
    }

    public SupportException(SupportExceptionEnum exceptionEnum) {
        super(exceptionEnum.getMsg());
        this.code = exceptionEnum.getCode();
        this.msg = exceptionEnum.getMsg();
    }

    public SupportException(SupportExceptionEnum exceptionEnum, String msg) {
        super(msg);
        this.code = exceptionEnum.getCode();
        this.msg = msg;
    }
}
