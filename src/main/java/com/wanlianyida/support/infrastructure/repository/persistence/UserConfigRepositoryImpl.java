package com.wanlianyida.support.infrastructure.repository.persistence;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.wanlianyida.support.domain.model.entity.UserConfigEntity;
import com.wanlianyida.support.domain.repository.UserConfigRepository;
import com.wanlianyida.support.infrastructure.repository.mapper.UserConfigMapper;
import com.wanlianyida.support.infrastructure.repository.po.UserConfigPO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年07月25日 09:08
 */
@Repository
public class UserConfigRepositoryImpl implements UserConfigRepository {

    @Resource
    private UserConfigMapper userConfigMapper;

    @Override
    public boolean insert(UserConfigEntity entity) {
        return userConfigMapper.insert(BeanUtil.toBean(entity, UserConfigPO.class)) > 0;
    }

    @Override
    public boolean updateByUserId(UserConfigEntity entity) {
        UpdateWrapper<UserConfigPO> wrapper = new UpdateWrapper<>();
        wrapper.eq("user_base_id", entity.getUserBaseId())
                .eq("type", entity.getType());
        return userConfigMapper.update(BeanUtil.toBean(entity, UserConfigPO.class), wrapper) > 0;
    }

    @Override
    public List<UserConfigEntity> queryByUserId(String userId, String type) {
        QueryWrapper<UserConfigPO> wrapper = new QueryWrapper<>();
        wrapper.eq("user_base_id", userId)
                .eq("type", type)
                .eq("del_flag", "0");
        return BeanUtil.copyToList(userConfigMapper.selectList(wrapper), UserConfigEntity.class);
    }
}
