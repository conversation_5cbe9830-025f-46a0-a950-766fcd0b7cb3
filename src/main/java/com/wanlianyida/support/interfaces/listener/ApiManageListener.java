package com.wanlianyida.support.interfaces.listener;

import com.alibaba.fastjson.JSONObject;
import com.wanlianyida.support.application.command.AuthLogCommand;
import com.wanlianyida.support.application.service.ApiManageAppService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年06月26日 14:31
 */
@Slf4j
@Component
public class ApiManageListener {

    @Resource
    private ApiManageAppService apiManageAppService;

    /**
     * 网关鉴权日志
     */
    @KafkaListener(id = "authLogListener", topics = "api_auth_log_topic", groupId = "api_auth_log_group", idIsGroup = false)
    public void authLogListener(@Payload String message) {
        AuthLogCommand command = JSONObject.parseObject(message, AuthLogCommand.class);
        apiManageAppService.insertAuthLog(command);
    }
}
