package com.wanlianyida.support.domain.model.condition;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年06月24日 17:51
 */
@Data
@ApiModel("接口管理表")
public class ApiManageCondition {

    @ApiModelProperty("产品类型[lgi_app_driver(司机端),lgi_app_shipper(货主端),lgi_3pl(web端),lgi_4pl(web端)]")
    private String productType;

    @ApiModelProperty("服务名称")
    private String serviceName;

    @ApiModelProperty("接口uri")
    private String apiUri;

    @ApiModelProperty("接口名称")
    private String apiName;

    @ApiModelProperty("接口归属人")
    private String apiOwner;

    @ApiModelProperty("接口调用方")
    private String apiCaller;

    @ApiModelProperty("启用标志[0-禁用,1-启用]")
    private Integer enableFlag;
}
