package com.wanlianyida.support.domain.model.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年06月24日 17:52
 */
@Data
@ApiModel("签名配置表")
public class SignConfigEntity {

    private Long id;

    @ApiModelProperty("配置key[客户端:client_web,client_android,client_ios,client_wechat_mp;内部服务:service_ctsp]")
    private String configKey;

    @ApiModelProperty("密钥")
    private String accessKeySecret;

    @ApiModelProperty("启用标志[0-禁用,1-启用]")
    private Integer enableFlag;

    @ApiModelProperty("签名描述")
    private String signDesc;

    private String creatorId;

    private Date createdDate;

    private String updaterId;

    private Date updatedDate;
}
