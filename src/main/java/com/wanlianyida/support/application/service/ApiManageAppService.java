package com.wanlianyida.support.application.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wanlianyida.framework.lgicache.impl.RedisService;
import com.wanlianyida.framework.lgicommon.entity.PagingInfo;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import com.wanlianyida.framework.lgicommon.model.command.IdCommand;
import com.wanlianyida.framework.lgicommon.model.query.IdQuery;
import com.wanlianyida.support.application.assembler.ApiManageAssembler;
import com.wanlianyida.support.application.command.ApiManageInsertCommand;
import com.wanlianyida.support.application.command.ApiManageUpdateCommand;
import com.wanlianyida.support.application.command.AuthLogCommand;
import com.wanlianyida.support.application.dto.ApiManageCacheDTO;
import com.wanlianyida.support.application.dto.ApiManageDTO;
import com.wanlianyida.support.application.dto.ApiVersionRecordDTO;
import com.wanlianyida.support.application.query.ApiManageQuery;
import com.wanlianyida.support.domain.model.bo.ApiManageBO;
import com.wanlianyida.support.domain.model.condition.ApiManageCondition;
import com.wanlianyida.support.domain.model.entity.ApiManageEntity;
import com.wanlianyida.support.domain.model.entity.ApiVersionRecordEntity;
import com.wanlianyida.support.domain.model.entity.AuthLogEntity;
import com.wanlianyida.support.domain.service.ApiManageDomainService;
import com.wanlianyida.support.infrastructure.constant.RedisKeyConstant;
import com.wanlianyida.support.infrastructure.exception.SupportException;
import com.wanlianyida.support.infrastructure.exception.SupportExceptionEnum;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年06月26日 09:52
 */
@Service
public class ApiManageAppService {

    @Resource
    private ApiManageDomainService apiManageDomainService;
    @Resource
    private RedisService redisService;

    /**
     * 新增接口
     */
    public void insertApi(ApiManageInsertCommand  command) {
        ApiManageBO apiManageBO = ApiManageAssembler.insertApi(command);
        apiManageDomainService.insertApi(apiManageBO);
        // 添加缓存
        redisService.hSet(RedisKeyConstant.REDIS_KEY_API_MANAGE, apiManageBO.getApiManage().getApiUri(), JSONObject.toJSONString(BeanUtil.toBean(apiManageBO.getApiManage(), ApiManageCacheDTO.class)));
    }

    /**
     * 修改接口
     */
    public void updateApi(ApiManageUpdateCommand command) {
        ApiManageEntity query = apiManageDomainService.queryById(command.getId());
        if (query == null) {
            throw new SupportException(SupportExceptionEnum.API_NOT_FOUND);
        }
        ApiManageBO apiManageBO = ApiManageAssembler.updateApi(command, query);
        ApiManageEntity update = apiManageDomainService.updateApi(apiManageBO);
        if (query.getEnableFlag().equals(1) && update.getEnableFlag().equals(0)) {
            // 删除缓存
            redisService.hDel(RedisKeyConstant.REDIS_KEY_API_MANAGE, query.getApiUri());
        } else {
            // 更新缓存
            ApiManageEntity newData = apiManageDomainService.queryById(query.getId());
            redisService.hSet(RedisKeyConstant.REDIS_KEY_API_MANAGE, newData.getApiUri(), JSONObject.toJSONString(BeanUtil.toBean(newData, ApiManageCacheDTO.class)));
        }
    }

    /**
     * 删除接口
     */
    public void deleteApi(IdCommand command) {
        ApiManageEntity query = apiManageDomainService.queryById(command.getId());
        if (query == null) {
            throw new SupportException(SupportExceptionEnum.API_NOT_FOUND);
        }
        apiManageDomainService.deleteApi(command.getId());
        // 删除缓存
        redisService.hDel(RedisKeyConstant.REDIS_KEY_API_MANAGE, query.getApiUri());
    }

    public ResultMode<ApiManageDTO> pageCondition(PagingInfo<ApiManageQuery> pageQuery) {
        Page<ApiManageEntity> page = PageHelper.startPage(pageQuery.getCurrentPage(), pageQuery.getPageLength(), true);
        page.setOrderBy("updated_date desc");
        List<ApiManageEntity> list = apiManageDomainService.queryCondition(BeanUtil.toBean(pageQuery.getFilterModel(), ApiManageCondition.class));
        return ResultMode.success(BeanUtil.copyToList(list, ApiManageDTO.class), (int) page.getTotal());
    }

    public ResultMode<ApiVersionRecordDTO> pageRecord(PagingInfo<IdQuery> pageQuery) {
        Page<ApiVersionRecordEntity> page = PageHelper.startPage(pageQuery.getCurrentPage(), pageQuery.getPageLength(), true);
        page.setOrderBy("created_date desc");
        List<ApiVersionRecordEntity> list = apiManageDomainService.queryRecordList(pageQuery.getFilterModel().getId());
        return ResultMode.success(BeanUtil.copyToList(list, ApiVersionRecordDTO.class), (int) page.getTotal());
    }

    public void insertAuthLog(AuthLogCommand command) {
        if (StrUtil.isNotBlank(command.getRequestParam()) && command.getRequestParam().length() > 500) {
            command.setRequestParam(command.getRequestParam().substring(0, 500));
        }
        apiManageDomainService.insertAuthLog(BeanUtil.toBean(command, AuthLogEntity.class));
    }
}
