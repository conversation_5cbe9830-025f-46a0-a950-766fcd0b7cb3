package com.wanlianyida.support.infrastructure.repository.persistence;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.wanlianyida.support.domain.model.condition.ApiManageCondition;
import com.wanlianyida.support.domain.model.entity.ApiManageEntity;
import com.wanlianyida.support.domain.model.entity.ApiVersionRecordEntity;
import com.wanlianyida.support.domain.repository.ApiManageRepository;
import com.wanlianyida.support.infrastructure.repository.mapper.ApiManageMapper;
import com.wanlianyida.support.infrastructure.repository.mapper.ApiVersionRecordMapper;
import com.wanlianyida.support.infrastructure.repository.po.ApiManagePO;
import com.wanlianyida.support.infrastructure.repository.po.ApiVersionRecordPO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年06月25日 15:29
 */
@Repository
public class ApiManageRepositoryImpl implements ApiManageRepository {

    @Resource
    private ApiManageMapper apiManageMapper;

    @Resource
    private ApiVersionRecordMapper apiVersionRecordMapper;


    @Override
    public boolean insertApi(ApiManageEntity entity) {
        ApiManagePO po = BeanUtil.toBean(entity, ApiManagePO.class);
        boolean result = apiManageMapper.insert(po) > 0;
        entity.setId(po.getId());
        return result;
    }

    @Override
    public boolean updateApi(ApiManageEntity entity) {
        return apiManageMapper.updateById(BeanUtil.toBean(entity, ApiManagePO.class)) > 0;
    }

    @Override
    public boolean deleteApi(Long id) {
        ApiManagePO po = new ApiManagePO();
        po.setId(id);
        po.setDelFlag(1);
        po.setUpdatedDate(new Date());
        return apiManageMapper.updateById(po) > 0;
    }

    @Override
    public ApiManageEntity queryById(Long id) {
        return BeanUtil.toBean(apiManageMapper.selectById(id), ApiManageEntity.class);
    }

    @Override
    public List<ApiManageEntity> queryCondition(ApiManageCondition condition) {
        QueryWrapper<ApiManagePO> wrapper = new QueryWrapper<>();
        if (StrUtil.isNotBlank(condition.getApiName())) {
            wrapper.like("api_name", condition.getApiName());
        }
        if (StrUtil.isNotBlank(condition.getApiUri())) {
            wrapper.like("api_uri", condition.getApiUri());
        }
        if (StrUtil.isNotBlank(condition.getApiOwner())) {
            wrapper.eq("api_owner", condition.getApiOwner());
        }
        if (StrUtil.isNotBlank(condition.getApiCaller())) {
            wrapper.eq("api_caller", condition.getApiCaller());
        }
        if (StrUtil.isNotBlank(condition.getProductType())) {
            wrapper.eq("product_type", condition.getProductType());
        }
        if (condition.getEnableFlag() != null) {
            wrapper.eq("enable_flag", condition.getEnableFlag());
        }
        wrapper.eq("del_flag", 0);
        return BeanUtil.copyToList(apiManageMapper.selectList(wrapper), ApiManageEntity.class);
    }

    @Override
    public List<ApiVersionRecordEntity> queryRecordList(Long id) {
        QueryWrapper<ApiVersionRecordPO> wrapper = new QueryWrapper<>();
        wrapper.eq("api_id", id);
        return BeanUtil.copyToList(apiVersionRecordMapper.selectList(wrapper), ApiVersionRecordEntity.class);
    }

    @Override
    public boolean insertRecord(ApiVersionRecordEntity entity) {
        return apiVersionRecordMapper.insert(BeanUtil.toBean(entity, ApiVersionRecordPO.class)) > 0;
    }
}
