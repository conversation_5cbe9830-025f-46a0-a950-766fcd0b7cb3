package com.wanlianyida.support.infrastructure.repository.persistence;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wanlianyida.support.domain.model.condition.SignConfigCondition;
import com.wanlianyida.support.domain.model.entity.SignConfigEntity;
import com.wanlianyida.support.domain.repository.SignConfigRepository;
import com.wanlianyida.support.infrastructure.repository.mapper.SignConfigMapper;
import com.wanlianyida.support.infrastructure.repository.po.SignConfigPO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年06月25日 15:31
 */
@Repository
public class SignConfigRepositoryImpl implements SignConfigRepository {

    @Resource
    private SignConfigMapper signConfigMapper;

    @Override
    public boolean insertSignConfig(SignConfigEntity entity) {
        return signConfigMapper.insert(BeanUtil.toBean(entity, SignConfigPO.class)) > 0;
    }

    @Override
    public boolean updateSignConfig(SignConfigEntity entity) {
        return signConfigMapper.updateById(BeanUtil.toBean(entity, SignConfigPO.class)) > 0;
    }

    @Override
    public boolean deleteSignConfig(Long id) {
        return signConfigMapper.deleteById(id) > 0;
    }

    @Override
    public SignConfigEntity queryById(Long id) {
        return BeanUtil.toBean(signConfigMapper.selectById(id), SignConfigEntity.class);
    }

    @Override
    public List<SignConfigEntity> queryCondition(SignConfigCondition condition) {
        QueryWrapper<SignConfigPO> wrapper = new QueryWrapper<>();
        if (StrUtil.isNotBlank(condition.getConfigKey())) {
            wrapper.eq("config_key", condition.getConfigKey());
        }
        if (condition.getEnableFlag() != null) {
            wrapper.eq("enable_flag", condition.getEnableFlag());
        }
        return BeanUtil.copyToList(signConfigMapper.selectList(wrapper), SignConfigEntity.class);
    }
}
