package com.wanlianyida.support.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年06月24日 17:52
 */
@Data
@ApiModel("接口版本记录表")
@TableName("plf_bd_api_version_record")
public class ApiVersionRecordPO extends Model<ApiVersionRecordPO> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty("接口id")
    private Long apiId;

    @ApiModelProperty("变更内容")
    private String alterContent;

    @ApiModelProperty("变更版本号")
    private String alterVersion;

    private String creatorId;

    private Date createdDate;

    private String updaterId;

    private String updaterName;

    private Date updatedDate;
}
