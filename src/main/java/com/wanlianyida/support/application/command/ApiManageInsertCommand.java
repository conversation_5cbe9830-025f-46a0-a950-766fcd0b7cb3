package com.wanlianyida.support.application.command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年06月24日 17:51
 */
@Data
@ApiModel("接口管理表")
public class ApiManageInsertCommand {

    @ApiModelProperty("产品类型[lgi_app_driver(司机端),lgi_app_shipper(货主端),lgi_3pl(web端),lgi_4pl(web端)]")
    @NotNull(message = "产品类型不能为空")
    private String productType;

    @ApiModelProperty("服务名称")
    @NotBlank(message = "服务名称不能为空")
    private String serviceName;

    @ApiModelProperty("接口uri")
    @NotBlank(message = "接口uri不能为空")
    private String apiUri;

    @ApiModelProperty("接口名称")
    @NotBlank(message = "接口名称不能为空")
    private String apiName;

    @ApiModelProperty("接口描述")
    @Length(max = 50, message = "接口描述不能超过50个字符")
    private String apiDesc;

    @ApiModelProperty("接口归属人")
    @NotBlank(message = "接口归属人不能为空")
    private String apiOwner;

    @ApiModelProperty("需要token鉴权标志[10-不需要,20-需要]")
    @NotNull(message = "需要token鉴权标志不能为空")
    private Integer needTokenAuthFlag;

    @ApiModelProperty("需要签名验证标志[10-不需要,20-需要]")
    @NotNull(message = "需要签名验证标志不能为空")
    private Integer needSignVerifyFlag;

    @ApiModelProperty("接口调用方")
    @NotBlank(message = "接口调用方不能为空")
    private String apiCaller;

    @ApiModelProperty("接口最小版本号")
    @NotBlank(message = "接口最小版本号不能为空")
    private String apiMinVersion;

    @ApiModelProperty("接口最大版本号")
    @NotBlank(message = "接口最大版本号不能为空")
    private String apiMaxVersion;

    @ApiModelProperty("接口当前版本号")
    @NotBlank(message = "接口当前版本号不能为空")
    private String apiCurVersion;
}
