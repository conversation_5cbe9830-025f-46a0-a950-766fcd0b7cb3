package com.wanlianyida.support.application.command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年06月24日 17:51
 */
@Data
@ApiModel("接口管理表")
public class ApiManageUpdateCommand {

    @NotNull(message = "接口id不能为空")
    private Long id;

    @ApiModelProperty("接口名称")
    private String apiName;

    @ApiModelProperty("接口描述")
    private String apiDesc;

    @ApiModelProperty("变更内容")
    private String alterContent;

    @ApiModelProperty("接口归属人")
    @NotBlank(message = "接口归属人不能为空")
    private String apiOwner;

    @ApiModelProperty("需要token鉴权标志[10-不需要,20-需要]")
    @NotNull(message = "需要token鉴权标志不能为空")
    private Integer needTokenAuthFlag;

    @ApiModelProperty("需要签名验证标志[10-不需要,20-需要]")
    @NotNull(message = "需要签名验证标志不能为空")
    private Integer needSignVerifyFlag;

    @ApiModelProperty("接口调用方")
    @NotBlank(message = "接口调用方不能为空")
    private String apiCaller;

    @ApiModelProperty("接口最小版本号")
    @NotBlank(message = "接口最小版本号不能为空")
    private String apiMinVersion;

    @ApiModelProperty("接口最大版本号")
    @NotBlank(message = "接口最大版本号不能为空")
    private String apiMaxVersion;

    @ApiModelProperty("接口当前版本号")
    @NotBlank(message = "接口当前版本号不能为空")
    private String apiCurVersion;

    @ApiModelProperty("修改人姓名")
    @NotBlank(message = "修改人姓名不能为空")
    private String updateName;
}
