package com.wanlianyida.support.domain.service;

import cn.hutool.core.collection.CollectionUtil;
import com.wanlianyida.support.domain.model.entity.UserConfigEntity;
import com.wanlianyida.support.domain.repository.UserConfigRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年07月25日 10:44
 */
@Service
public class UserConfigDomainService {

    @Resource
    private UserConfigRepository userConfigRepository;

    /**
     * 保存工作台时间范围
     */
    public void saveWorkbenchDateScope(UserConfigEntity  entity) {
        List<UserConfigEntity> userConfigEntity = userConfigRepository.queryByUserId(entity.getUserBaseId(), entity.getType());
        if (CollectionUtil.isEmpty(userConfigEntity)) {
            Date now = new Date();
            entity.setCreateBy(entity.getUserBaseId());
            entity.setUpdateBy(entity.getUserBaseId());
            entity.setCreateDate(now);
            entity.setUpdateDate(now);
            userConfigRepository.insert(entity);
        } else {
            userConfigRepository.updateByUserId(entity);
        }
    }

    /**
     * 查询用户配置
     */
    public List<UserConfigEntity> queryByUserId(String userBaseId, String type) {
        return userConfigRepository.queryByUserId(userBaseId, type);
    }
}
