package com.wanlianyida.support.application.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年06月24日 17:52
 */
@Data
@ApiModel("签名配置表")
public class SignConfigCacheDTO {

    @ApiModelProperty("配置key[客户端:client_web,client_android,client_ios,client_wechat_mp;内部服务:service_ctsp]")
    private String configKey;

    @ApiModelProperty("密钥")
    private String accessKeySecret;
}
